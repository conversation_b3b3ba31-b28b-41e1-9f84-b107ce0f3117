#define _GNU_SOURCE
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <sys/time.h>
#include <pthread.h>
#include <sys/epoll.h>
#include <poll.h>
#include <time.h>
#include <signal.h>
#include <sched.h>
#include "mycan.h"

char ver[20] = {"ver02.001"};

int sock_fd;
struct_can_param can_param = {250000, 0x103, {
                                                 0x100,
                                                 0x00,
                                             },
                              0,
                              0,
                              0,
                              2000000,
                              0.75,
                              0.75};

// 数据收发应用
// 数据收发应用
int active_send_mode = 0;
int loopback_send_mode = 0, devs = 1;
unsigned long present_time = 0;
struct_canx canx[MAX_LINE_NUMB] = {0};
pthread_t can_thread[MAX_LINE_NUMB] = {0};
int cpus = 1;
int main_control_signal_fd[8][2];
static int kfd = 0; // 终端描述符
static struct termios cooked, raw;
static int terminal_initialized = 0; // 标记终端是否已初始化
int no_check = 0;

// 退出时恢复终端参数的函数
void restore_terminal_on_exit(void)
{
    if (terminal_initialized && kfd >= 0)
    {
        tcsetattr(kfd, TCSAFLUSH, &cooked);
        terminal_initialized = 0;
    }
}

/*
 * @description : 自定义打印函数
 * @param - buff:  打印数据缓冲区
 * @param - lens:  打印数据长度
 * @param - mode:  打印格式
 * @return		: 无
 */
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    printf("\n");
}
unsigned long func_get_system_time_ms(void)
{
    struct timespec time = {0, 0};

    clock_gettime(CLOCK_MONOTONIC, &time);
    return (time.tv_sec * 1000 + time.tv_nsec / 1000000);
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s [-devs numb] device0~device7 [-b baudrate] [-s sample] [-id id_numb] [-filt id mask] [-e] [-fd dbaudrate] [-ds dsample]  [-t0 len0 time0]~[-t7 len7 time7] [-c0 times0]~[-c7 times7] [-l] [-nc] [-L] [-v]"
           "\n\t'-devs numb' for device number, range 1 to 8, default 1"
           "\n\t'-b baudrate' for different baudrate, range 5k to 5M,unit is Kbps"
           "\n\t'-s sample' for different sample point, range 0 to 99,"
           "\n\t'-id id_numb' for nonzero can send id"
           "\n\t'-filt id mask' for receive can_id and can_mask"
           "\n\t'-e' for extend id frame"
           "\n\t'-fd dbaudrate' for can fd mode , dbaudrate range 250k to 8M,unit is Kbps"
           "\n\t'-ds dsample' for different data sample point, range 0 to 99,"
           "\n\t'-t len time'  for interval set time actively sends the len data, unit is ms ; range 0 to 7"
           "\n\t'-c times'  for total send times; range 0 to 7"
           "\n\t'-l' for can loopback mode"
           "\n\t'-L' for app loopback receive data"
           "\n\t'-nc' for no check"
           "\n\t'-v' show version"
           "\n\texample : can0 baudrate 250k extend id 400 Bytes was send every 2s--> ./can_demo can0 -b 250 -e -t 400 2000"
           "\n\texample : can1 baudrate 250k only receive --> ./can_demo can1 -b 250"
           "\n\texample : can0 canfd mode baudrate 500k extend id dbaudrate 4000k forward the received data out --> ./can_demo can0 -b 500 -fd 4000 -e -L\n ",
           pname);
}

/*
 * @description : 解析函数带入参数
 * @param - numb: 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *canparam: can应用参数
 * @return		: 无
 */
void get_param(int numb, char *param[], struct_can_param *canparam)
{
    int i = 0, len = 0, j = 0, k = 0, l;
    unsigned int baudrate = 0, id = 0, mask = 0;
    char temp_key1[5] = {0};
    char temp_key2[5] = {0};

    if (numb <= 2)
        return;

    for (i = 1; i < numb; i++)
    {
        if (!strcmp(param[i], "-devs"))
        {
            i++;
            len = atoi(param[i]);
            if ((len > 0) && (len < 9))
            {
                devs = len;
            }
            else
            {
                devs = 1;
            }
            for (j = 0; j < devs; j++)
            {
                strcpy(canx[j].dev, param[i + 1 + j]);
                printf("canx[%d].dev = %s \n", j, canx[j].dev);
                canx[j].numb = j;
            }
            continue;
        }
        if (!strcmp(param[i], "-b"))
        {
            i++;
            baudrate = atoi(param[i]);
            switch (baudrate)
            {
            case 5:
            case 10:
            case 20:
            case 40:
            case 50:
            case 80:
            case 100:
            case 125:
            case 200:
            case 250:
            case 400:
            case 500:
            case 666:
            case 800:
            case 1000:
            case 2000:
            case 4000:
            case 5000:
                canparam->baudrate = baudrate * 1000;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            i++;
            len = atoi(param[i]);
            if ((len > 0) && (len < 99))
                canparam->sample_point = (float)len / 100;
        }
        if (!strcmp(param[i], "-id"))
        {
            i++;
            id = strtoul(param[i], NULL, 16);
            if (id)
            {
                canparam->id = (unsigned int)id;
            }
            continue;
        }
        if (!strcmp(param[i], "-filt"))
        {
            i++;
            id = 0;
            id = strtoul(param[i], NULL, 16);
            canparam->filter.can_id = (canid_t)id;
            i++;
            mask = strtoul(param[i], NULL, 16);
            canparam->filter.can_mask = (canid_t)mask;
            continue;
        }
        if (!strcmp(param[i], "-e"))
        {
            canparam->extend = 1;
            continue;
        }
        if (!strcmp(param[i], "-fd"))
        {
            canparam->canfd_mode = CAN_FD_MODE;

            i++;
            baudrate = atoi(param[i]);
            switch (baudrate)
            {
            case 250:
            case 500:
            case 1000:
            case 2000:
            case 3000:
            case 4000:
            case 5000:
            case 6000:
            case 7000:
            case 8000:
                canparam->data_baudrate = baudrate * 1000;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-ds"))
        {
            i++;
            len = atoi(param[i]);
            if ((len > 0) && (len < 99))
                canparam->data_sample_point = (float)len / 100;
        }
        for (k = 0; k < devs; k++)
        {
            sprintf(temp_key1, "-t%d", k);
            sprintf(temp_key2, "-c%d", k);
            if (!strcmp(param[i], temp_key1))
            {
                active_send_mode = 1;

                i++;
                len = atoi(param[i]);
                // len = strlen(param[i]);
                if (len > 0)
                {
                    canx[k].active_send_num = len;
                    if (canx[k].active_send_num > MAX_BUFF_LEN)
                        canx[k].active_send_num = MAX_BUFF_LEN;
                    for (l = 0; l < canx[k].active_send_num; l++)
                        canx[k].send_buff[l] = l;

                    i++;
                    len = atoi(param[i]); // 发送间隔
                    if (len >= 0)
                    {
                        canx[k].active_send_time = len; // 转换为ms单位
                    }
                }
                continue;
            }
            if (!strcmp(param[i], temp_key2))
            {
                i++;
                len = atoi(param[i]);
                if (len > 0)
                {
                    canx[k].want_send_times = len;
                }
                continue;
            }
        }
        if (!strcmp(param[i], "-l"))
        {
            canparam->loopback_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-L"))
        {
            loopback_send_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-nc"))
        {
            no_check = 1;
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("can_demo ver:  %s\n", ver);
            continue;
        }
    }
}
/*
 * @description    : 给can线程发送信号,主要用途是通知线程回收资源
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void main_send_event_to_can(int event, int numb)
{
    if (write(main_control_signal_fd[numb][0], &event, sizeof(event)) == -1)
    {
        printf("send signal to can thread %d err: %s\n", numb, strerror(errno));
    }
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    int j = 0;

    // 立即恢复终端参数
    if (kfd >= 0)
    {
        tcsetattr(kfd, TCSAFLUSH, &cooked); // 使用TCSAFLUSH确保立即生效
    }

    for (j = 0; j < devs; j++)
    {
        main_send_event_to_can(MANAGE_QUITE_EVENT, j);
    }
    sleep(3);

    exit(1);
}
/*
 * @description : CAN收发管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *can_manage(void *arg)
{
    struct_canx *sockx;
    int triger_event = 0;
    int ne, ret, nevents = 0;
    int fd_temp = 0, revents = 0;
    int j = 0, k = 0;
    unsigned char rx_order = 0;
    unsigned char tx_order = 0;
    int timeout = 50;
    int real_send_num = 0;
    int t = 1000;

    sockx = (struct_canx *)arg;

    if (1 == active_send_mode)
        03 = sockx->active_send_time;

    sockx->deal_ok = 0;
    fcntl(sockx->sock_fd, F_SETFL, O_NONBLOCK); // 非阻塞

    struct pollfd pollfds1[2] = {{main_control_signal_fd[sockx->numb][1], POLLIN, 0},
                                 {sockx->sock_fd, POLLIN, 0}};
    nevents = 2;

    while (1)
    {
        do
        {
            ret = poll(pollfds1, nevents, timeout);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            // 停止测试
            goto __can_manage_quit;
        }

        if (ret == 0) // 超时
        {
            if (1 == active_send_mode)
            {
                sockx->send_num = sockx->active_send_num;
                for (j = 0; j < sockx->active_send_num; j++)
                {
                    sockx->send_buff[j] = tx_order;
                    tx_order++;
                }
            }
            else
            {
                continue;
            }
            if (can_param.canfd_mode == CAN_FD_MODE)
            {
                real_send_num = func_send_canfd_buff(sockx->sock_fd, sockx->send_buff, sockx->send_num, can_param, &sockx->total_send);
            }
            else
            {
                real_send_num = func_send_can_buff(sockx->sock_fd, sockx->send_buff, sockx->send_num, can_param, &sockx->total_send);
            }
            if (real_send_num > 0)
            {
                sockx->total_send_group++;
                if (sockx->want_send_times)
                {
                    if (sockx->total_send_group >= sockx->want_send_times)
                    {
                        while (t > 1)
                        {
                            if (can_param.canfd_mode == CAN_FD_MODE)
                            {
                                sockx->receive_num = func_receive_canfd_buff(sockx->sock_fd, sockx->receive_buff, sizeof(sockx->receive_buff), &sockx->total_receive);
                            }
                            else
                            {
                                sockx->receive_num = func_receive_can_buff(sockx->sock_fd, sockx->receive_buff, sizeof(sockx->receive_buff), &sockx->total_receive);
                            }
                            usleep(30);
                            t--;
                        }
                        // sockx->deal_ok = 1;
                        goto __can_manage_quit;
                    }
                }
            }
            memset(sockx->send_buff, 0, sockx->send_num);
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd_temp = pollfds1[ne].fd;
            revents = pollfds1[ne].revents;

            if (revents & (POLLERR | POLLHUP | POLLNVAL))
            {
                printf("can_manage[%d] error on fd %d: revents=0x%x\n", sockx->numb, fd_temp, revents);
                goto __can_manage_quit;
            }

            if ((revents & POLLIN) == 0)
            {
                continue;
            }
            if (fd_temp == main_control_signal_fd[sockx->numb][1])
            {
                ssize_t bytes_read = read(fd_temp, &triger_event, sizeof(triger_event));
                if (bytes_read == sizeof(triger_event))
                {
                    if (triger_event == MANAGE_QUITE_EVENT)
                    {
                        printf("can_manage[%d] receive quite signal\n", sockx->numb);
                        goto __can_manage_quit1;
                    }
                }
            }
            if (fd_temp == sockx->sock_fd) // 接收数据处理
            {
                if (can_param.canfd_mode == CAN_FD_MODE)
                {
                    sockx->receive_num = func_receive_canfd_buff(sockx->sock_fd, sockx->receive_buff, sizeof(sockx->receive_buff), &sockx->total_receive);
                }
                else
                {
                    sockx->receive_num = func_receive_can_buff(sockx->sock_fd, sockx->receive_buff, sizeof(sockx->receive_buff), &sockx->total_receive);
                }
                if (sockx->receive_num > 0)
                {
                    if (no_check == 0)
                    {
                        for (j = 0; j < sockx->receive_num; j++)
                        {
                            if (sockx->receive_buff[j] != rx_order)
                            {
                                printf("receive error %d: %d, %d \n", j, sockx->receive_buff[j], rx_order);
                                rx_order = sockx->receive_buff[j];
                                for (k = 0; k < sockx->receive_num; k++)
                                    printf("%d, ", sockx->receive_buff[k]);
                                printf("\n");
                            }
                            rx_order++;
                        }
                    }
                }
                // 组织发送数据
                if ((1 == loopback_send_mode) && (sockx->receive_num > 0)) // 数据回环处理
                {
                    sockx->send_num = sockx->receive_num;
                    memcpy(sockx->send_buff, sockx->receive_buff, sockx->receive_num);

                    // 发送数据
                    if (sockx->send_num > 0)
                    {
                        if (can_param.canfd_mode == CAN_FD_MODE)
                        {
                            real_send_num = func_send_canfd_buff(sockx->sock_fd, sockx->send_buff, sockx->send_num, can_param, &sockx->total_send);
                        }
                        else
                        {
                            real_send_num = func_send_can_buff(sockx->sock_fd, sockx->send_buff, sockx->send_num, can_param, &sockx->total_send);
                        }
                        sockx->send_num = 0;
                    }
                }
            }
        }
    }
__can_manage_quit1:

    while (t > 1)
    {
        if (can_param.canfd_mode == CAN_FD_MODE)
        {
            sockx->receive_num = func_receive_canfd_buff(sockx->sock_fd, sockx->receive_buff, sizeof(sockx->receive_buff), &sockx->total_receive);
        }
        else
        {
            sockx->receive_num = func_receive_can_buff(sockx->sock_fd, sockx->receive_buff, sizeof(sockx->receive_buff), &sockx->total_receive);
        }
        usleep(30);
        t--;
    }
    // printf("%s total_send=%ld, total_receive=%ld \n", sockx->dev, sockx->total_send, sockx->total_receive);

__can_manage_quit:
    printf("%s total_send=%ld, total_receive=%ld \n", sockx->dev, sockx->total_send, sockx->total_receive);
    close(sockx->sock_fd);
    usleep(100000);
    sockx->deal_ok = 1;
    return NULL; // 使用 return 而不是 exit，只退出当前线程
}
/*
 * @description : 获取cpu核数
 * @param - arg : 参数
 * @return		: 无
 */
int count_physical_cores(void)
{
    FILE *fp = fopen("/proc/cpuinfo", "r");
    if (!fp)
    {
        perror("Failed to open /proc/cpuinfo");
        return -1;
    }

    char line[256];
    int physical_cores = 0;

    while (fgets(line, sizeof(line), fp))
    {
        if (strstr(line, "processor"))
        {
            physical_cores++;
        }
    }

    fclose(fp);

    if (!physical_cores)
    {
        fp = fopen("nproc", "r");
        if (!fp)
        {
            perror("Failed to open nproc");
            return -1;
        }

        while (fgets(line, sizeof(line), fp))
        {
            if (sscanf(line, "%d", &physical_cores) == 1)
            {
                break;
            }
        }
        fclose(fp);
    }
    return physical_cores;
}
int bind_thread_to_cpu(pthread_t thread, int cpu_id)
{
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);        // 清空CPU集合
    CPU_SET(cpu_id, &cpuset); // 添加指定的CPU核心

    // 设置线程的CPU亲和性
    int ret = pthread_setaffinity_np(thread, sizeof(cpu_set_t), &cpuset);
    if (ret != 0)
    {
        perror("pthread_setaffinity_np failed");
        return -1;
    }
    return 0;
}
// 验证线程是否成功绑定到指定核心
int verify_thread_binding(pthread_t thread)
{
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);

    int result = pthread_getaffinity_np(thread, sizeof(cpu_set_t), &cpuset);
    if (result != 0)
    {
        fprintf(stderr, "Failed to acquire thread affinity: %s\n", strerror(result));
        return -1;
    }

    printf("The CPU core bound to the thread: ");
    int bound_cpu = -1;
    for (int i = 0; i < CPU_SETSIZE; i++)
    {
        if (CPU_ISSET(i, &cpuset))
        {
            printf("%d ", i);
            if (bound_cpu == -1)
                bound_cpu = i; // 记录第一个绑定的CPU
        }
    }
    printf("\n");

    return bound_cpu;
}
/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0, j = 0;
    int numb_cpus = 0;
    int all_send_ok = 0;

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有参数
    if (argc < 2 || ((strncmp(argv[1], "can", 3) == 0) && strncmp(argv[1], "-devs", 5) == 0))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 从main函数带来的参数解析为CAN口参数
    get_param(argc, argv, &can_param);

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);
    // 添加更多信号处理确保终端能被恢复
    signal(SIGQUIT, SignHandler);
    signal(SIGHUP, SignHandler);  // 终端断开
    signal(SIGABRT, SignHandler); // 异常终止

    // 当知道设备名称时可以直接赋值dev，例strcpy(dev, "can0");
    // 打开CAN口 创建socket 绑定socket
    if (can_param.canfd_mode == CAN_FD_MODE)
    {
        result = func_open_canfd(devs, can_param, &j);
        if (result < 0)
        {
            printf("Can't Open deveice %s \n", canx[j].dev);
            goto __ym_exit;
        }
    }
    else
    {
        result = func_open_can(devs, can_param, &j);
        if (result < 0)
        {
            printf("Can't Open deveice %s \n", canx[j].dev);
            goto __ym_exit;
        }
    }
    // 设置CAN口过滤
    if (can_param.canfd_mode == CAN_FD_MODE)
    {
        for (j = 0; j < devs; j++)
        {
            result = func_set_canfd(canx[j].sock_fd, can_param);
            if (result < 0)
            {
                perror("set_opt error");
                goto __ym_exit;
            }
        }
    }
    else
    {
        for (j = 0; j < devs; j++)
        {
            result = func_set_can(canx[j].sock_fd, can_param);
            if (result < 0)
            {
                perror("set_opt error");
                goto __ym_exit;
            }
        }
    }
    // 设置CAN口为非阻塞方式
    // for (j = 0; j < devs; j++)
    //   fcntl(canx[j].sock_fd, F_SETFL, O_NONBLOCK); // 非阻塞

    // 创建线程
    cpus = count_physical_cores();
    if (!cpus)
        cpus = 1;
    for (j = 0; j < devs; j++)
    {
        // 先创建socketpair，再创建线程
        if (socketpair(AF_LOCAL, SOCK_STREAM, 0, main_control_signal_fd[j]) < 0)
        {
            printf("%s Failed to create control_signal_fd for thread %d: %d (%s)", __func__, j, errno, strerror(errno));
            goto __ym_exit2;
        }
        printf("Created socketpair for thread %d: fd[0]=%d, fd[1]=%d\n",
               j, main_control_signal_fd[j][0], main_control_signal_fd[j][1]);

        if (pthread_create(&can_thread[j], NULL, can_manage, (void *)&canx[j]))
        {
            printf("Pthread %d can_manage create error\n", j);
            goto __ym_exit2;
        }

        numb_cpus = j % cpus;
        // 绑定线程到指定CPU核心
        if (bind_thread_to_cpu(can_thread[j], numb_cpus) != 0)
        {
            fprintf(stderr, "thread %d Binding to the core failed\n", j);
        }
        result = verify_thread_binding(can_thread[j]);
        if (-1 == result)
            fprintf(stderr, "thread %d Binding to the core failed\n", j);
        else
            printf("thread %d Bound successfully to the CPU core %d\n", j, result);

        // 将线程设置为分离状态
        if (pthread_detach(can_thread[j]) != 0)
        {
            perror("pthread_detach");
            goto __ym_exit2;
        }

        printf("can_thread[%d] created\n", j);
    }
    char ch = 0;

    // 注册退出时恢复终端的函数
    atexit(restore_terminal_on_exit);

    tcgetattr(kfd, &cooked); // 获取当前终端参数
    memcpy(&raw, &cooked, sizeof(struct termios));
    raw.c_lflag &= ~(ICANON | ECHO); // 屏蔽整行缓存
    raw.c_cc[VEOL] = 1;
    raw.c_cc[VEOF] = 2;
    tcsetattr(kfd, TCSANOW, &raw); // 设置新的终端参数
    terminal_initialized = 1;      // 标记终端已初始化

    char cmd[200] = {0};

    while (1)
    {
        if (read(kfd, &ch, 1) < 0) // 读取键盘值
        {
            perror("read():");
            goto __ym_exit1;
        }
        if (ch == ' ')
        {
            // 按下空格键后打印一次收发数量和系统状态
            for (j = 0; j < devs; j++)
            {
                printf("%s total_send=%ld, total_receive=%ld \n", canx[j].dev, canx[j].total_send, canx[j].total_receive);
                sprintf(cmd, "ip -details -statistics link show %s", canx[j].dev);
                system(cmd);
            }
            system("top -n 1");
        }
        all_send_ok = 1;
        for (j = 0; j < devs; j++)
        {
            if (canx[j].deal_ok == 0)
            {
                all_send_ok = 0;
                break; // 有一个通道没有发送完成，则不退出，继续等待发送完成
            }
        }
        if (all_send_ok)
            goto __ym_exit1;

        sleep(1);
    }
__ym_exit1:

    // 立即恢复终端参数
    if (kfd >= 0)
    {
        tcsetattr(kfd, TCSAFLUSH, &cooked); // 使用TCSAFLUSH确保立即生效
    }

__ym_exit2:
    for (j = 0; j < devs; j++)
    {
        main_send_event_to_can(MANAGE_QUITE_EVENT, j);
    }
    sleep(3);

__ym_exit:
    // 再次确保终端参数被恢复
    if (kfd >= 0)
    {
        tcsetattr(kfd, TCSAFLUSH, &cooked);
    }
    printf("程序退出完成\n");
    exit(0);
}