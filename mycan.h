#ifndef MYCAN_H
#define MYCAN_H

#include <linux/can.h>

#define CAN_MODE 0
#define CAN_FD_MODE 1
#define MAX_BUFF_LEN 2200
#define MAX_LINE_NUMB 8
#define MANAGE_QUITE_EVENT 0x01

/*CAN口参数结构体 */
typedef struct
{
    unsigned long baudrate;      /*波特率 5k~1000k*/
    unsigned int id;             /*设备ID*/
    struct can_filter filter;    /*接收设备过滤ID*/
    unsigned char extend;        /*扩展ID*/
    unsigned char loopback_mode; /*回环模式*/

    unsigned char canfd_mode;    /*CANFD模式*/
    unsigned long data_baudrate; /*CANFD模式下需要单独设置数据波特率*/
    float sample_point;
    float data_sample_point;
} struct_can_param;

typedef struct
{
    int sock_fd;
    int numb;
    char dev[20];
    unsigned char send_buff[MAX_BUFF_LEN];
    unsigned char receive_buff[MAX_BUFF_LEN];
    unsigned char tx_order;
    unsigned char rx_order;
    unsigned int send_num;
    unsigned int receive_num;
    unsigned int active_send_num;
    unsigned int active_send_time;
    unsigned long total_send_group;
    unsigned long want_send_times;
    unsigned long total_send;
    unsigned long total_receive;
    int deal_ok;
    unsigned long last_time;
} struct_canx;

extern struct_canx canx[8];
int func_open_can(int numb, struct_can_param para, int *x);
int func_set_can(int fd, struct_can_param para);
int func_receive_can_buff(int fd, unsigned char *buff, int len, unsigned long *total);
int func_send_can_buff(int fd, unsigned char *buff, int len, struct_can_param param, unsigned long *total);

int func_open_canfd(int numb, struct_can_param para, int *x);
int func_set_canfd(int fd, struct_can_param para);
int func_receive_canfd_buff(int fd, unsigned char *buff, int len, unsigned long *total);
int func_send_canfd_buff(int fd, unsigned char *buff, int len, struct_can_param param, unsigned long *total);
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode);
#endif