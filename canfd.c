#include <errno.h>
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include "mycan.h"

int count_all = 0;
int err_all = 0;

static const unsigned char dlc2len[] = {0, 1, 2, 3, 4, 5, 6, 7,
                                        8, 12, 16, 20, 24, 32, 48, 64};

/* get data length from raw data length code (DLC) */
unsigned char can_fd_dlc2len(unsigned char dlc)
{
    return dlc2len[dlc & 0x0F];
}

static const unsigned char len2dlc[] = {0, 1, 2, 3, 4, 5, 6, 7, 8,       /* 0 - 8 */
                                        8, 8, 8, 8,                      /* 9 - 12 */
                                        9, 9, 9, 9,                      /* 13 - 16 */
                                        10, 10, 10, 10,                  /* 17 - 20 */
                                        11, 11, 11, 11,                  /* 21 - 24 */
                                        12, 12, 12, 12, 12, 12, 12, 12,  /* 25 - 32 */
                                        13, 13, 13, 13, 13, 13, 13, 13,  /* 33 - 40 */
                                        13, 13, 13, 13, 13, 13, 13, 13,  /* 41 - 48 */
                                        14, 14, 14, 14, 14, 14, 14, 14,  /* 49 - 56 */
                                        14, 14, 14, 14, 14, 14, 14, 14}; /* 57 - 63 */

/* map the sanitized data length to an appropriate data length code */
unsigned char can_fd_len2dlc(unsigned char len)
{
    if (len > 64)
        return 0xF;

    return len2dlc[len];
}

/*
 * @description        : 字符串按格式输出
 * @param - *destpoint : 字符串格式缓冲区
 * @param - *fmt       : 多个参数
 * @return		       : 按格式输出字符串缓冲区首指针
 */
char func_dprintf1(char *destpoint, char *fmt, ...)
{
    va_list arg_ptr;
    char ulen, *tmpBuf;

    tmpBuf = destpoint;
    va_start(arg_ptr, fmt);
    ulen = vsprintf(tmpBuf, fmt, arg_ptr);

    va_end(arg_ptr);
    return ulen;
}

/*
 * @description     : 打开canfd外设，设置波特率，创建文件描述符
 * @param - numb    : 设备个数
 * @param - para    : canfd应用参数
 * @return		    : 打开设备执成功返回文件描述符，失败返回-1
 */
int func_open_canfd(int numb, struct_can_param para, int *x)
{
    FILE *fstream = NULL;
    char buff[300] = {0}, command[300] = {0};
    struct sockaddr_can addr;
    struct ifreq ifr;
    int i = 0;

    /*关闭can设备 ifconfig can0 down*/
    for (i = 0; i < numb; i++)
    {
        memset(buff, 0, sizeof(buff));
        memset(command, 0, sizeof(command));
        func_dprintf1(command, "ifconfig %s down", canx[i].dev);
        printf("%s \n", command);

        if (NULL == (fstream = popen(command, "w")))
        {
            fprintf(stderr, "execute command failed: %s", strerror(errno));
        }
        while (NULL != fgets(buff, sizeof(buff), fstream))
        {
            printf("%s\n", buff);
            if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            {
                pclose(fstream);
                *x = i;
                return -1;
            }
        }
        pclose(fstream);
    }
    sleep(1);

    /*设置can波特率 ip link set can0 up type can bitrate 250000 dbitrate 4000000 fd on*/
    for (i = 0; i < numb; i++)
    {
        memset(buff, 0, sizeof(buff));
        memset(command, 0, sizeof(command));
        func_dprintf1(command, "ip link set %s up type can bitrate %d sample-point %f dbitrate %d dsample-point %f fd on loopback off", canx[i].dev, para.baudrate, para.sample_point, para.data_baudrate, para.data_sample_point);
        printf("%s \n", command);
        if (NULL == (fstream = popen(command, "w")))
        {
            fprintf(stderr, "execute command failed: %s", strerror(errno));
        }
        while (NULL != fgets(buff, sizeof(buff), fstream))
        {
            printf("%s\n", buff);
            if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            {
                pclose(fstream);
                *x = i;
                return -1;
            }
        }
        pclose(fstream);
    }
    sleep(1);

    /*设置can设备 发送队列为1000*/
    for (i = 0; i < numb; i++)
    {
        memset(buff, 0, sizeof(buff));
        memset(command, 0, sizeof(command));
        func_dprintf1(command, "ip link set %s txqueuelen 1000", canx[i].dev);
        printf("%s \n", command);

        if (NULL == (fstream = popen(command, "w")))
        {
            fprintf(stderr, "execute command failed: %s", strerror(errno));
        }
        while (NULL != fgets(buff, sizeof(buff), fstream))
        {
            printf("%s\n", buff);
            if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            {
                pclose(fstream);
                *x = i;
                return -1;
            }
        }
        pclose(fstream);
    }
    sleep(1);

    /*打开can设备 ifconfig can0 up*/
    for (i = 0; i < numb; i++)
    {
        memset(buff, 0, sizeof(buff));
        memset(command, 0, sizeof(command));
        func_dprintf1(command, "ifconfig %s up", canx[i].dev);
        printf("%s \n", command);

        if (NULL == (fstream = popen(command, "w")))
        {
            fprintf(stderr, "execute command failed: %s", strerror(errno));
        }
        while (NULL != fgets(buff, sizeof(buff), fstream))
        {
            printf("%s\n", buff);
            if (strstr(buff, "No such device") || strstr(buff, "Cannot find device"))
            {
                pclose(fstream);
                *x = i;
                return -1;
            }
        }
        pclose(fstream);
    }
    sleep(3);

    int rcvbuf_size = 1024 * 1024; // 设置为1MB，根据你的需求调整这个值
    socklen_t optlen = sizeof(rcvbuf_size);
    for (i = 0; i < numb; i++)
    {
        /* 创建 socket */
        canx[i].sock_fd = socket(PF_CAN, SOCK_RAW, CAN_RAW);
        if (canx[i].sock_fd < 0)
        {
            printf("socket:%s", strerror(errno));
            *x = i;
            return -1;
        }

        /* 设置接口设备名称 */
        strcpy(ifr.ifr_name, canx[i].dev);
        /* 确定接口 index */
        ifr.ifr_ifindex = if_nametoindex(ifr.ifr_name);
        if (!ifr.ifr_ifindex)
        {
            printf("if_nametoindex:%s\n", strerror(errno));
            *x = i;
            return -1;
        }

        memset(&addr, 0, sizeof(addr));
        addr.can_family = AF_CAN;
        addr.can_ifindex = ifr.ifr_ifindex;

        /* 绑定 socket到 CAN 接口 */
        if (bind(canx[i].sock_fd, (struct sockaddr *)&addr, sizeof(addr)) < 0)
        {
            printf("bind:%s\n", strerror(errno));
            *x = i;
            return -1;
        }
        if (setsockopt(canx[i].sock_fd, SOL_SOCKET, SO_RCVBUF, &rcvbuf_size, optlen) < 0)
        {
            perror("setsockopt SO_RCVBUF");
            // 错误处理
        }
    }
    return 1;
}

/*
 * @description : 设置canfd模式，回环模式和过滤规则
 * @param - fd  : 文件描述符
 * @param - para: canfd应用参数
 * @return		: 设置成功返回1，失败返回-1
 */
int func_set_canfd(int fd, struct_can_param para)
{
    int enable_canfd = 1;
    // int loopback = 1;
    // int reciveown = 1;

    // 设置FD模式
    if (setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FD_FRAMES, &enable_canfd, sizeof(enable_canfd)) < 0)
    {
        printf("error when enabling CAN FD support\n");
        return -1;
    }
    /* 设置过滤规则 */
    if (setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FILTER, &para.filter, sizeof(para.filter)) < 0)
    {
        printf("error when set filter\n");
        return -1;
    }
    return 1;
}

/*
 * @description    : canfd接收一个canfd帧
 * @param - fd     : 文件描述符
 * @param - *pframe: 一个canfd帧结构体指针
 * @return		   : 接收数据长度
 */
int func_receive_canfd_frame(int fd, struct canfd_frame *pframe, unsigned long *total_receive)
{
    int rx_count = 0;

    rx_count = recv(fd, pframe, sizeof(*pframe), 0);
    if (rx_count <= 0)
    {
        return rx_count;
    }
    else
    {
        if (pframe->can_id & CAN_EFF_FLAG) /*如果是扩展帧，清除扩展帧标识*/
        {
            pframe->can_id &= (~CAN_EFF_FLAG);
        }
        else
        {
            pframe->can_id &= (~CAN_SFF_MASK);
        }
    }
    total_receive++;
    // printf("can_id=0x%08x,can_len=%d data : ", pframe->can_id, pframe->len);
    // func_my_print(pframe->data, pframe->len, 'h'); // 将收到的数据打印出来

    return pframe->len;
}

/*
 * @description  : canfd接收一组数据包
 * @param - fd   : 文件描述符
 * @param - *buff: 要接收canfd一组数据的缓冲区首指针
 * @param - len  : 接收到一组数据的长度
 * @return		 : 接收数据长度
 */
int func_receive_canfd_buff(int fd, unsigned char *buff, int len, unsigned long *total)
{
    int receive_len = 0, total_receive_len = 0;
    struct canfd_frame frame;
    int i = 0;

    while (1)
    {
        receive_len = func_receive_canfd_frame(fd, &frame, total);
        for (i = 0; i < receive_len; i++)
        {
            *(buff + total_receive_len) = frame.data[i];
            total_receive_len++;
        }
        if ((receive_len <= 0) || (total_receive_len > (len - 64)))
        {
            return total_receive_len;
        }
    }

    return total_receive_len;
}

/*
 * @description    : canfd发送一个canfd帧
 * @param - fd     : 文件描述符
 * @param - *pframe: 一个canfd帧结构体指针
 * @param - param  : canfd应用参数
 * @return		   : 发送数据长度，发送失败返回-1
 */
int func_send_canfd_frame(int fd, struct canfd_frame *pframe, struct_can_param param, unsigned long *total)
{
    int result = 0;

    // printf("can_id=0x%08x,can_len=%d data : ", pframe->can_id, pframe->len);
    if (param.extend == 1) /*扩展帧增加扩展帧标志*/
    {
        pframe->can_id &= CAN_EFF_MASK;
        pframe->can_id |= CAN_EFF_FLAG;
    }
    else
    {
        pframe->can_id &= CAN_SFF_MASK;
    }
    pframe->flags |= 0x01; // 置位1发送帧按照帧速率，数据按照数据帧速率的帧，清为0整帧忽略数据帧速率，按照帧速率发送
    result = send(fd, pframe, sizeof(struct canfd_frame), 0);
    if (result == -1)
    {
        printf("send:%s\n", strerror(errno));
        return -1;
    }
    // func_my_print(pframe->data, pframe->len, 'h'); // 将收到的数据打印出来
    (*total)++;
    return result;
}
/*注意：canfd的数据虽然能发送0-64个数据，但是数据长度仅能是0, 1, 2, 3, 4, 5, 6, 7, 8, 12, 16, 20, 24, 32, 48, 64
  这几个档，例如，若想发送9个数据，实际发送的是12个数据，后3个数据补0，回环测试，接收也是12个数。为解决收发不对应的问题有如下2种方案
  1.占用ID的某7位，附上实际发送长度。接收端根据实际长度处理接收的数据，
  2.将发送的数据拆分，例9个数，先发8个数据，再发1个数据，这样接收端无需特殊处理，下述程序按照方案2实现
  当然，如果实际应用规约中有数据长度或起始帧，结束帧，也可以不用在应用上特殊处理分帧，因为补0肯定会在帧末尾，通过规约解析可以排除掉末尾0数据*/
/*
 * @description  : canfd发送一组数据包
 * @param - fd   : 文件描述符
 * @param - *buff: 要发送canfd一组数据的缓冲区首指针
 * @param - len  : 发送数据的长度
 * @param - param: canfd应用参数
 * @return		 : 实际发送数据长度
 */
int func_send_canfd_buff(int fd, unsigned char *buff, int len, struct_can_param param, unsigned long *total)
{
    int remain_frame_len = 0, send_frame_len = 0;
    struct canfd_frame frame;
    int i = 0;

    remain_frame_len = len;
    while (1)
    {
        memset(&frame, 0, sizeof(struct canfd_frame));
        if (remain_frame_len >= 64)
        {
            frame.len = 64;
            remain_frame_len -= 64; // 总发送数据-每帧发送数量
        }
        else
        {
            /* ensure discrete CAN FD length values 0..8, 12, 16, 20, 24, 32, 64 */
            frame.len = can_fd_dlc2len(can_fd_len2dlc(remain_frame_len));
            remain_frame_len -= frame.len; //
        }
        // printf("frame_len=%d,remain_frame_len=%d,send_frame_len=%d\n", frame.len, remain_frame_len, send_frame_len);

        frame.can_id = param.id;
        for (i = 0; i < frame.len; i++)
        {
            frame.data[i] = buff[send_frame_len + i];
        }
        send_frame_len += frame.len;
        func_send_canfd_frame(fd, &frame, param, total);
        if (remain_frame_len == 0)
        {
            return len;
        }
    }
    return len;
}