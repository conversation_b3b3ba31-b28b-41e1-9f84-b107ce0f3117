#335xD
# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#A40i
# CC=/usr/opt/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-gcc 
#3568
#CC=aarch64-linux-gnu-gcc
#d9
#CC=/tool/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc

can_per_check:main.o can.o canfd.o
	$(CC)	-Wall	main.o can.o canfd.o -o	can_per_check  -lpthread
main.o:main.c mycan.h 
	$(CC)	-c	-Wall	main.c	-o	main.o  -lpthread
can.o:can.c mycan.h
	$(CC)	-c	-Wall	can.c	-o	can.o
canfd.o:canfd.c mycan.h
	$(CC)	-c	-Wall	canfd.c	-o	canfd.o
clean:
	$(RM) *.o	can_per_check

